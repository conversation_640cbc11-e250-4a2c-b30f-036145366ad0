// =====================================================
// Accommodations Service Layer
// =====================================================
// Service functions for accommodation CRUD operations and image management

import { supabase } from "./supabase";
import type {
  Accommodation,
  AccommodationCreateRequest,
  AccommodationUpdateRequest,
  AccommodationFilters,
  AccommodationSort,
  AccommodationListResponse,
  AccommodationResponse,
  ImageUploadResponse,
  BulkAccommodationOperation,
  BulkOperationResponse,
} from "@/types/accommodation";

// =====================================================
// CRUD Operations
// =====================================================

/**
 * Get accommodations with filtering, sorting, and pagination
 */
export async function getAccommodations(
  filters: AccommodationFilters,
  sort: AccommodationSort,
  pagination: { page: number; limit: number }
): Promise<AccommodationListResponse> {
  try {
    let query = supabase.from("accommodations").select("*", { count: "exact" });

    // Apply filters
    if (filters.search) {
      query = query.or(
        `name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`
      );
    }

    if (filters.type !== "all") {
      query = query.eq("type", filters.type);
    }

    if (filters.status !== "all") {
      query = query.eq("status", filters.status);
    }

    if (filters.featured !== "all") {
      query = query.eq("featured", filters.featured);
    }

    // Apply sorting
    query = query.order(sort.field, { ascending: sort.direction === "asc" });

    // Apply pagination
    const from = (pagination.page - 1) * pagination.limit;
    const to = from + pagination.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / pagination.limit);

    return {
      data: data || [],
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: count || 0,
        totalPages,
      },
      filters,
      sort,
    };
  } catch (error) {
    console.error("Error fetching accommodations:", error);
    throw error;
  }
}

/**
 * Get a single accommodation by ID
 */
export async function getAccommodation(
  id: string
): Promise<AccommodationResponse> {
  try {
    const { data, error } = await supabase
      .from("accommodations")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching accommodation:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch accommodation",
    };
  }
}

/**
 * Create a new accommodation
 */
export async function createAccommodation(
  accommodationData: AccommodationCreateRequest
): Promise<AccommodationResponse> {
  try {
    const { data, error } = await supabase
      .from("accommodations")
      .insert([accommodationData])
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Accommodation created successfully",
    };
  } catch (error) {
    console.error("Error creating accommodation:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to create accommodation",
    };
  }
}

/**
 * Update an existing accommodation
 */
export async function updateAccommodation(
  accommodationData: AccommodationUpdateRequest
): Promise<AccommodationResponse> {
  try {
    const { id, ...updateData } = accommodationData;

    const { data, error } = await supabase
      .from("accommodations")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Accommodation updated successfully",
    };
  } catch (error) {
    console.error("Error updating accommodation:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update accommodation",
    };
  }
}

/**
 * Delete an accommodation
 */
export async function deleteAccommodation(
  id: string
): Promise<{ success: boolean; message: string }> {
  try {
    // First, get the accommodation to retrieve image URLs for cleanup
    const { data: accommodation } = await supabase
      .from("accommodations")
      .select("images")
      .eq("id", id)
      .single();

    // Delete the accommodation record
    const { error } = await supabase
      .from("accommodations")
      .delete()
      .eq("id", id);

    if (error) throw error;

    // Clean up associated images
    if (accommodation?.images && Array.isArray(accommodation.images)) {
      await Promise.all(
        accommodation.images.map((imageUrl: string) =>
          deleteAccommodationImage(imageUrl)
        )
      );
    }

    return {
      success: true,
      message: "Accommodation deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting accommodation:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to delete accommodation",
    };
  }
}

// =====================================================
// Status Management
// =====================================================

/**
 * Update accommodation status
 */
export async function updateAccommodationStatus(
  id: string,
  status: "draft" | "published" | "unpublished"
): Promise<AccommodationResponse> {
  return updateAccommodation({ id, status });
}

/**
 * Toggle accommodation featured status
 */
export async function toggleAccommodationFeatured(
  id: string,
  featured: boolean
): Promise<AccommodationResponse> {
  return updateAccommodation({ id, featured });
}

// =====================================================
// Image Management
// =====================================================

/**
 * Upload accommodation image to Supabase Storage
 */
export async function uploadAccommodationImage(
  file: File,
  accommodationId: string
): Promise<ImageUploadResponse> {
  try {
    // Generate unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `${accommodationId}/${Date.now()}.${fileExt}`;

    const { error } = await supabase.storage
      .from("accommodation-images")
      .upload(fileName, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) throw error;

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from("accommodation-images").getPublicUrl(fileName);

    return {
      url: publicUrl,
      path: fileName,
      success: true,
    };
  } catch (error) {
    console.error("Error uploading image:", error);
    return {
      url: "",
      path: "",
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to upload image",
    };
  }
}

/**
 * Delete accommodation image from Supabase Storage
 */
export async function deleteAccommodationImage(
  imageUrl: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Extract file path from URL
    const url = new URL(imageUrl);
    const pathParts = url.pathname.split("/");
    const fileName = pathParts[pathParts.length - 1];
    const folderName = pathParts[pathParts.length - 2];
    const filePath = `${folderName}/${fileName}`;

    const { error } = await supabase.storage
      .from("accommodation-images")
      .remove([filePath]);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error("Error deleting image:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to delete image",
    };
  }
}

// =====================================================
// Bulk Operations
// =====================================================

/**
 * Perform bulk operations on accommodations
 */
export async function bulkUpdateAccommodations(
  operation: BulkAccommodationOperation
): Promise<BulkOperationResponse> {
  try {
    let updateData: Partial<Accommodation> = {};

    switch (operation.action) {
      case "publish":
        updateData = { status: "published" };
        break;
      case "unpublish":
        updateData = { status: "unpublished" };
        break;
      case "feature":
        updateData = { featured: true };
        break;
      case "unfeature":
        updateData = { featured: false };
        break;
      case "delete":
        // Handle delete separately
        const deletePromises = operation.accommodation_ids.map((id) =>
          deleteAccommodation(id)
        );
        const deleteResults = await Promise.all(deletePromises);
        const successCount = deleteResults.filter((r) => r.success).length;
        const failedCount = deleteResults.length - successCount;

        return {
          success: failedCount === 0,
          updated_count: successCount,
          failed_count: failedCount,
          message: `${successCount} accommodations deleted successfully${
            failedCount > 0 ? `, ${failedCount} failed` : ""
          }`,
        };
    }

    const { data, error } = await supabase
      .from("accommodations")
      .update(updateData)
      .in("id", operation.accommodation_ids)
      .select("id");

    if (error) throw error;

    return {
      success: true,
      updated_count: data?.length || 0,
      failed_count: 0,
      message: `${data?.length || 0} accommodations updated successfully`,
    };
  } catch (error) {
    console.error("Error performing bulk operation:", error);
    return {
      success: false,
      updated_count: 0,
      failed_count: operation.accommodation_ids.length,
      message: error instanceof Error ? error.message : "Bulk operation failed",
    };
  }
}

// =====================================================
// Public API (for frontend website)
// =====================================================

/**
 * Get published accommodations for public display
 */
export async function getPublishedAccommodations(): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from("accommodations_public")
      .select("*")
      .order("featured", { ascending: false })
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching published accommodations:", error);
    return [];
  }
}

/**
 * Get featured accommodations for homepage
 */
export async function getFeaturedAccommodations(): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from("accommodations_public")
      .select("*")
      .eq("featured", true)
      .order("created_at", { ascending: false })
      .limit(6);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching featured accommodations:", error);
    return [];
  }
}
