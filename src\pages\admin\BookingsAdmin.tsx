import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Calendar, User, Mail, Phone, Eye } from "lucide-react";

export default function BookingsAdmin() {
  const bookings = [
    {
      id: 1,
      guestName: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8900",
      checkIn: "2024-09-15",
      checkOut: "2024-09-18",
      accommodation: "Safari Suite",
      status: "Confirmed",
      total: "$1,200",
    },
    {
      id: 2,
      guestName: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8901",
      checkIn: "2024-09-20",
      checkOut: "2024-09-23",
      accommodation: "Forest View Room",
      status: "Pending",
      total: "$900",
    },
    {
      id: 3,
      guestName: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8902",
      checkIn: "2024-09-10",
      checkOut: "2024-09-12",
      accommodation: "Riverside Tent",
      status: "Completed",
      total: "$600",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Confirmed":
        return "bg-green-100 text-green-800";
      case "Pending":
        return "bg-yellow-100 text-yellow-800";
      case "Completed":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bookings</h1>
          <p className="text-gray-600 mt-2">
            Manage guest reservations and bookings
          </p>
        </div>
        <Button className="bg-amber-600 hover:bg-amber-700">
          <Plus className="w-4 h-4 mr-2" />
          New Booking
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {bookings.map((booking) => (
          <Card key={booking.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg flex items-center">
                    <User className="w-5 h-5 mr-2 text-gray-600" />
                    {booking.guestName}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Booking #{booking.id.toString().padStart(4, "0")}
                  </CardDescription>
                </div>
                <Badge className={getStatusColor(booking.status)}>
                  {booking.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Contact
                  </div>
                  <div className="text-sm text-gray-600 flex items-center mt-1">
                    <Mail className="w-4 h-4 mr-1" />
                    {booking.email}
                  </div>
                  <div className="text-sm text-gray-600 flex items-center mt-1">
                    <Phone className="w-4 h-4 mr-1" />
                    {booking.phone}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Dates</div>
                  <div className="text-sm text-gray-600 flex items-center mt-1">
                    <Calendar className="w-4 h-4 mr-1" />
                    {booking.checkIn} to {booking.checkOut}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    Accommodation
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {booking.accommodation}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Total</div>
                  <div className="text-lg font-bold text-amber-600 mt-1">
                    {booking.total}
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-4">
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Booking Statistics</CardTitle>
          <CardDescription>Overview of reservations this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">18</div>
              <div className="text-sm text-gray-500">Confirmed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">6</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">12</div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">$28,400</div>
              <div className="text-sm text-gray-500">Total Revenue</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
