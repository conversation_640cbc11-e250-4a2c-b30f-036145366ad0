-- =====================================================
-- Malombo Booking Management System - Stored Procedures
-- =====================================================
-- Comprehensive stored procedures for booking operations

-- =====================================================
-- Booking Management Functions
-- =====================================================

-- Get bookings with filtering, sorting, and pagination
CREATE OR REPLACE FUNCTION get_bookings_filtered(
    search_term TEXT DEFAULT NULL,
    status_filter VARCHAR(50) DEFAULT NULL,
    payment_filter VARCHAR(50) DEFAULT NULL,
    accommodation_filter UUID DEFAULT NULL,
    source_filter VARCHAR(50) DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL,
    sort_by VARCHAR(50) DEFAULT 'check_in',
    sort_order VARCHAR(4) DEFAULT 'desc',
    page_limit INTEGER DEFAULT 10,
    page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    guest_full_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    country VARCHAR(100),
    adults INTEGER,
    children_5_13 INTEGER,
    children_0_4 INTEGER,
    accommodation_id UUID,
    accommodation_name VARCHAR(255),
    accommodation_type VARCHAR(100),
    check_in DATE,
    check_out DATE,
    nights INTEGER,
    currency VARCHAR(3),
    total_amount DECIMAL(10,2),
    status VARCHAR(50),
    payment_status VARCHAR(50),
    source VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
DECLARE
    query_text TEXT;
    where_conditions TEXT[] := '{}';
    order_clause TEXT;
BEGIN
    -- Build WHERE conditions
    IF search_term IS NOT NULL AND search_term != '' THEN
        where_conditions := array_append(where_conditions, 
            format('(b.guest_full_name ILIKE %L OR b.email ILIKE %L OR b.phone ILIKE %L OR b.id::text ILIKE %L)', 
                   '%' || search_term || '%', '%' || search_term || '%', 
                   '%' || search_term || '%', '%' || search_term || '%'));
    END IF;
    
    IF status_filter IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.status = %L', status_filter));
    END IF;
    
    IF payment_filter IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.payment_status = %L', payment_filter));
    END IF;
    
    IF accommodation_filter IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.accommodation_id = %L', accommodation_filter));
    END IF;
    
    IF source_filter IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.source = %L', source_filter));
    END IF;
    
    IF date_from IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.check_in >= %L', date_from));
    END IF;
    
    IF date_to IS NOT NULL THEN
        where_conditions := array_append(where_conditions, format('b.check_in <= %L', date_to));
    END IF;
    
    -- Build ORDER BY clause
    order_clause := format('ORDER BY b.%I %s', sort_by, 
                          CASE WHEN upper(sort_order) = 'ASC' THEN 'ASC' ELSE 'DESC' END);
    
    -- Build complete query
    query_text := format('
        SELECT 
            b.id,
            b.guest_full_name,
            b.email,
            b.phone,
            b.country,
            b.adults,
            b.children_5_13,
            b.children_0_4,
            b.accommodation_id,
            a.name as accommodation_name,
            a.type as accommodation_type,
            b.check_in,
            b.check_out,
            b.nights,
            b.currency,
            b.total_amount,
            b.status,
            b.payment_status,
            b.source,
            b.created_at,
            COUNT(*) OVER() as total_count
        FROM bookings b
        LEFT JOIN accommodations a ON b.accommodation_id = a.id
        %s
        %s
        LIMIT %s OFFSET %s',
        CASE WHEN array_length(where_conditions, 1) > 0 
             THEN 'WHERE ' || array_to_string(where_conditions, ' AND ')
             ELSE '' END,
        order_clause,
        page_limit,
        page_offset
    );
    
    RETURN QUERY EXECUTE query_text;
END;
$$ LANGUAGE plpgsql;

-- Get booking details with related data
CREATE OR REPLACE FUNCTION get_booking_details(booking_id UUID)
RETURNS TABLE (
    booking_data JSONB,
    accommodation_data JSONB,
    activities_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(b.*) as booking_data,
        to_jsonb(a.*) as accommodation_data,
        COALESCE(
            (SELECT jsonb_agg(to_jsonb(act.*))
             FROM activities act
             WHERE act.id = ANY(b.activity_ids)),
            '[]'::jsonb
        ) as activities_data
    FROM bookings b
    LEFT JOIN accommodations a ON b.accommodation_id = a.id
    WHERE b.id = booking_id;
END;
$$ LANGUAGE plpgsql;

-- Update booking status with history tracking
CREATE OR REPLACE FUNCTION update_booking_status(
    booking_id UUID,
    new_status VARCHAR(50),
    admin_notes TEXT DEFAULT NULL
)
RETURNS TABLE (success BOOLEAN, message TEXT) AS $$
DECLARE
    old_status VARCHAR(50);
    booking_exists BOOLEAN;
BEGIN
    -- Check if booking exists and get current status
    SELECT b.status, TRUE INTO old_status, booking_exists
    FROM bookings b WHERE b.id = booking_id;
    
    IF NOT booking_exists THEN
        RETURN QUERY SELECT FALSE, 'Booking not found';
        RETURN;
    END IF;
    
    -- Validate status transition
    IF old_status = new_status THEN
        RETURN QUERY SELECT FALSE, 'Booking is already in this status';
        RETURN;
    END IF;
    
    -- Update booking status
    UPDATE bookings 
    SET status = new_status,
        notes_admin = CASE WHEN admin_notes IS NOT NULL 
                          THEN COALESCE(notes_admin, '') || E'\n' || admin_notes 
                          ELSE notes_admin END,
        updated_at = NOW()
    WHERE id = booking_id;
    
    RETURN QUERY SELECT TRUE, format('Booking status updated from %s to %s', old_status, new_status);
END;
$$ LANGUAGE plpgsql;

-- Update payment status
CREATE OR REPLACE FUNCTION update_payment_status(
    booking_id UUID,
    new_payment_status VARCHAR(50),
    payment_method_val VARCHAR(100) DEFAULT NULL,
    payment_ref VARCHAR(255) DEFAULT NULL
)
RETURNS TABLE (success BOOLEAN, message TEXT) AS $$
DECLARE
    booking_exists BOOLEAN;
BEGIN
    -- Check if booking exists
    SELECT TRUE INTO booking_exists FROM bookings WHERE id = booking_id;
    
    IF NOT booking_exists THEN
        RETURN QUERY SELECT FALSE, 'Booking not found';
        RETURN;
    END IF;
    
    -- Update payment information
    UPDATE bookings 
    SET payment_status = new_payment_status,
        payment_method = COALESCE(payment_method_val, payment_method),
        payment_reference = COALESCE(payment_ref, payment_reference),
        updated_at = NOW()
    WHERE id = booking_id;
    
    RETURN QUERY SELECT TRUE, format('Payment status updated to %s', new_payment_status);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Statistics and Analytics Functions
-- =====================================================

-- Get booking statistics
CREATE OR REPLACE FUNCTION get_booking_statistics(
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS TABLE (
    total_bookings BIGINT,
    confirmed_bookings BIGINT,
    pending_bookings BIGINT,
    cancelled_bookings BIGINT,
    completed_bookings BIGINT,
    total_revenue DECIMAL(10,2),
    paid_revenue DECIMAL(10,2),
    pending_revenue DECIMAL(10,2),
    average_booking_value DECIMAL(10,2),
    total_guests INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_bookings,
        COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed_bookings,
        COUNT(*) FILTER (WHERE status = 'new') as pending_bookings,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_bookings,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_bookings,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(SUM(total_amount) FILTER (WHERE payment_status = 'paid'), 0) as paid_revenue,
        COALESCE(SUM(total_amount) FILTER (WHERE payment_status IN ('unpaid', 'deposit_paid')), 0) as pending_revenue,
        COALESCE(AVG(total_amount), 0) as average_booking_value,
        COALESCE(SUM(adults + children_5_13 + children_0_4), 0)::INTEGER as total_guests
    FROM bookings
    WHERE (date_from IS NULL OR check_in >= date_from)
      AND (date_to IS NULL OR check_in <= date_to)
      AND status != 'cancelled';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Pricing and Availability Functions
-- =====================================================

-- Calculate booking totals with taxes
CREATE OR REPLACE FUNCTION calculate_booking_totals(
    base_amount DECIMAL(10,2),
    currency_code VARCHAR(3) DEFAULT 'USD'
)
RETURNS TABLE (
    base_amount_out DECIMAL(10,2),
    tax_amount DECIMAL(10,2),
    service_charge DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    tax_rate DECIMAL(5,2),
    service_rate DECIMAL(5,2)
) AS $$
DECLARE
    pricing_settings JSONB;
    tax_rate_val DECIMAL(5,2);
    service_rate_val DECIMAL(5,2);
    tax_amt DECIMAL(10,2);
    service_amt DECIMAL(10,2);
BEGIN
    -- Get pricing settings
    SELECT value INTO pricing_settings 
    FROM settings 
    WHERE key = 'pricing_defaults';
    
    -- Extract rates
    tax_rate_val := COALESCE((pricing_settings->>'tax_rate')::DECIMAL(5,2), 0);
    service_rate_val := COALESCE((pricing_settings->>'service_charge')::DECIMAL(5,2), 0);
    
    -- Calculate amounts
    tax_amt := base_amount * (tax_rate_val / 100);
    service_amt := base_amount * (service_rate_val / 100);
    
    RETURN QUERY SELECT 
        base_amount as base_amount_out,
        tax_amt as tax_amount,
        service_amt as service_charge,
        base_amount + tax_amt + service_amt as total_amount,
        tax_rate_val as tax_rate,
        service_rate_val as service_rate;
END;
$$ LANGUAGE plpgsql;

-- Check accommodation availability
CREATE OR REPLACE FUNCTION check_accommodation_availability(
    accommodation_id UUID,
    check_in_date DATE,
    check_out_date DATE,
    exclude_booking_id UUID DEFAULT NULL
)
RETURNS TABLE (
    available BOOLEAN,
    conflicting_bookings JSONB
) AS $$
DECLARE
    conflicts JSONB;
BEGIN
    -- Get conflicting bookings
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', id,
            'guest_name', guest_full_name,
            'check_in', check_in,
            'check_out', check_out,
            'status', status
        )
    ), '[]'::jsonb) INTO conflicts
    FROM bookings
    WHERE accommodation_id = check_accommodation_availability.accommodation_id
      AND status NOT IN ('cancelled')
      AND (exclude_booking_id IS NULL OR id != exclude_booking_id)
      AND daterange(check_in, check_out, '[]') && 
          daterange(check_in_date, check_out_date, '[]');
    
    RETURN QUERY SELECT 
        (jsonb_array_length(conflicts) = 0) as available,
        conflicts as conflicting_bookings;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Settings Management Functions
-- =====================================================

-- Get all settings
CREATE OR REPLACE FUNCTION get_all_settings()
RETURNS TABLE (
    key VARCHAR(255),
    value JSONB,
    description TEXT,
    category VARCHAR(100),
    is_public BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    updated_by UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value, s.description, s.category, s.is_public,
           s.created_at, s.updated_at, s.updated_by
    FROM settings s
    ORDER BY s.category, s.key;
END;
$$ LANGUAGE plpgsql;

-- Get public settings only
CREATE OR REPLACE FUNCTION get_public_settings()
RETURNS TABLE (
    key VARCHAR(255),
    value JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value
    FROM settings s
    WHERE s.is_public = TRUE
    ORDER BY s.key;
END;
$$ LANGUAGE plpgsql;

-- Get settings by category
CREATE OR REPLACE FUNCTION get_settings_by_category(category_name VARCHAR(100))
RETURNS TABLE (
    key VARCHAR(255),
    value JSONB,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value, s.description, s.updated_at
    FROM settings s
    WHERE s.category = category_name
    ORDER BY s.key;
END;
$$ LANGUAGE plpgsql;

-- Get specific setting value
CREATE OR REPLACE FUNCTION get_setting_value(setting_key VARCHAR(255))
RETURNS JSONB AS $$
DECLARE
    setting_value JSONB;
BEGIN
    SELECT value INTO setting_value
    FROM settings
    WHERE key = setting_key;

    RETURN COALESCE(setting_value, 'null'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Update setting
CREATE OR REPLACE FUNCTION update_setting(
    setting_key VARCHAR(255),
    setting_value JSONB
)
RETURNS TABLE (success BOOLEAN, message TEXT) AS $$
BEGIN
    -- Insert or update setting
    INSERT INTO settings (key, value, updated_at)
    VALUES (setting_key, setting_value, NOW())
    ON CONFLICT (key)
    DO UPDATE SET
        value = EXCLUDED.value,
        updated_at = EXCLUDED.updated_at;

    RETURN QUERY SELECT TRUE, format('Setting %s updated successfully', setting_key);
END;
$$ LANGUAGE plpgsql;

-- Bulk update settings
CREATE OR REPLACE FUNCTION bulk_update_settings(settings_data JSONB)
RETURNS INTEGER AS $$
DECLARE
    setting_key TEXT;
    setting_value JSONB;
    update_count INTEGER := 0;
BEGIN
    -- Iterate through settings object
    FOR setting_key, setting_value IN SELECT * FROM jsonb_each(settings_data)
    LOOP
        INSERT INTO settings (key, value, updated_at)
        VALUES (setting_key, setting_value, NOW())
        ON CONFLICT (key)
        DO UPDATE SET
            value = EXCLUDED.value,
            updated_at = EXCLUDED.updated_at;

        update_count := update_count + 1;
    END LOOP;

    RETURN update_count;
END;
$$ LANGUAGE plpgsql;

-- Update email template
CREATE OR REPLACE FUNCTION update_email_template(
    template_name VARCHAR(255),
    template_data JSONB
)
RETURNS TABLE (success BOOLEAN, message TEXT) AS $$
DECLARE
    current_templates JSONB;
    updated_templates JSONB;
BEGIN
    -- Get current email templates
    SELECT value INTO current_templates
    FROM settings
    WHERE key = 'email_templates';

    -- Update specific template
    updated_templates := jsonb_set(
        COALESCE(current_templates, '{}'::jsonb),
        ARRAY[template_name],
        template_data
    );

    -- Save back to settings
    UPDATE settings
    SET value = updated_templates, updated_at = NOW()
    WHERE key = 'email_templates';

    RETURN QUERY SELECT TRUE, format('Email template %s updated successfully', template_name);
END;
$$ LANGUAGE plpgsql;

-- Get notification contacts
CREATE OR REPLACE FUNCTION get_notification_contacts(notification_type VARCHAR(100))
RETURNS TEXT[] AS $$
DECLARE
    email_settings JSONB;
    contacts TEXT[];
BEGIN
    -- Get email settings
    SELECT value INTO email_settings
    FROM settings
    WHERE key = 'email_settings';

    -- Extract notification contacts
    SELECT ARRAY(
        SELECT jsonb_array_elements_text(
            email_settings->'booking_notifications'->notification_type
        )
    ) INTO contacts;

    RETURN COALESCE(contacts, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Utility and Maintenance Functions
-- =====================================================

-- Clean up old booking status history (keep last 100 entries per booking)
CREATE OR REPLACE FUNCTION cleanup_booking_history()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH ranked_history AS (
        SELECT id,
               ROW_NUMBER() OVER (PARTITION BY booking_id ORDER BY changed_at DESC) as rn
        FROM booking_status_history
    )
    DELETE FROM booking_status_history
    WHERE id IN (
        SELECT id FROM ranked_history WHERE rn > 100
    );

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Get database statistics
CREATE OR REPLACE FUNCTION get_database_stats()
RETURNS TABLE (
    table_name TEXT,
    row_count BIGINT,
    table_size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        schemaname||'.'||tablename as table_name,
        n_tup_ins - n_tup_del as row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- Validate booking data integrity
CREATE OR REPLACE FUNCTION validate_booking_integrity()
RETURNS TABLE (
    booking_id UUID,
    issue_type TEXT,
    issue_description TEXT
) AS $$
BEGIN
    -- Check for bookings with invalid dates
    RETURN QUERY
    SELECT b.id, 'invalid_dates'::TEXT, 'Check-out date is not after check-in date'::TEXT
    FROM bookings b
    WHERE b.check_out <= b.check_in;

    -- Check for bookings with missing accommodations
    RETURN QUERY
    SELECT b.id, 'missing_accommodation'::TEXT, 'Referenced accommodation does not exist'::TEXT
    FROM bookings b
    LEFT JOIN accommodations a ON b.accommodation_id = a.id
    WHERE a.id IS NULL;

    -- Check for bookings with invalid amounts
    RETURN QUERY
    SELECT b.id, 'invalid_amounts'::TEXT, 'Total amount does not match calculated total'::TEXT
    FROM bookings b
    WHERE b.total_amount < 0 OR b.base_amount < 0;

    -- Check for overlapping confirmed bookings
    RETURN QUERY
    SELECT DISTINCT b1.id, 'overlapping_booking'::TEXT,
           format('Overlaps with booking %s', b2.id)::TEXT
    FROM bookings b1
    JOIN bookings b2 ON b1.accommodation_id = b2.accommodation_id
                    AND b1.id != b2.id
                    AND b1.status IN ('confirmed', 'completed')
                    AND b2.status IN ('confirmed', 'completed')
                    AND daterange(b1.check_in, b1.check_out, '[]') &&
                        daterange(b2.check_in, b2.check_out, '[]');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Search and Reporting Functions
-- =====================================================

-- Full-text search across bookings
CREATE OR REPLACE FUNCTION search_bookings(search_query TEXT)
RETURNS TABLE (
    id UUID,
    guest_full_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    accommodation_name VARCHAR(255),
    check_in DATE,
    check_out DATE,
    status VARCHAR(50),
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id,
        b.guest_full_name,
        b.email,
        b.phone,
        a.name as accommodation_name,
        b.check_in,
        b.check_out,
        b.status,
        ts_rank(
            to_tsvector('english', b.guest_full_name || ' ' || b.email || ' ' || b.phone || ' ' || COALESCE(a.name, '')),
            plainto_tsquery('english', search_query)
        ) as rank
    FROM bookings b
    LEFT JOIN accommodations a ON b.accommodation_id = a.id
    WHERE to_tsvector('english', b.guest_full_name || ' ' || b.email || ' ' || b.phone || ' ' || COALESCE(a.name, ''))
          @@ plainto_tsquery('english', search_query)
    ORDER BY rank DESC, b.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Generate revenue report
CREATE OR REPLACE FUNCTION generate_revenue_report(
    start_date DATE,
    end_date DATE,
    group_by_period VARCHAR(10) DEFAULT 'month'
)
RETURNS TABLE (
    period_start DATE,
    period_end DATE,
    total_bookings BIGINT,
    total_revenue DECIMAL(10,2),
    paid_revenue DECIMAL(10,2),
    pending_revenue DECIMAL(10,2),
    average_booking_value DECIMAL(10,2)
) AS $$
DECLARE
    date_trunc_format TEXT;
BEGIN
    -- Set date truncation format
    date_trunc_format := CASE
        WHEN group_by_period = 'day' THEN 'day'
        WHEN group_by_period = 'week' THEN 'week'
        WHEN group_by_period = 'year' THEN 'year'
        ELSE 'month'
    END;

    RETURN QUERY EXECUTE format('
        SELECT
            date_trunc(%L, b.check_in)::DATE as period_start,
            (date_trunc(%L, b.check_in) + interval %L - interval ''1 day'')::DATE as period_end,
            COUNT(*) as total_bookings,
            SUM(b.total_amount) as total_revenue,
            SUM(CASE WHEN b.payment_status = ''paid'' THEN b.total_amount ELSE 0 END) as paid_revenue,
            SUM(CASE WHEN b.payment_status IN (''unpaid'', ''deposit_paid'') THEN b.total_amount ELSE 0 END) as pending_revenue,
            AVG(b.total_amount) as average_booking_value
        FROM bookings b
        WHERE b.check_in >= %L
          AND b.check_in <= %L
          AND b.status != ''cancelled''
        GROUP BY date_trunc(%L, b.check_in)
        ORDER BY period_start',
        date_trunc_format,
        date_trunc_format,
        CASE
            WHEN group_by_period = 'day' THEN '1 day'
            WHEN group_by_period = 'week' THEN '1 week'
            WHEN group_by_period = 'year' THEN '1 year'
            ELSE '1 month'
        END,
        start_date,
        end_date,
        date_trunc_format
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Performance Optimization Functions
-- =====================================================

-- Analyze table statistics for query optimization
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS TEXT AS $$
BEGIN
    ANALYZE bookings;
    ANALYZE accommodations;
    ANALYZE activities;
    ANALYZE settings;
    ANALYZE booking_status_history;

    RETURN 'Table statistics updated successfully';
END;
$$ LANGUAGE plpgsql;
