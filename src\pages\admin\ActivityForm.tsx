import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Save,
  ArrowLeft,
  Upload,
  X,
  Check,
  ChevronsUpDown,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

import {
  createActivity,
  updateActivity,
  getActivity,
  uploadActivityImage,
} from "@/lib/activities";
import type { ActivityFormData } from "@/types/activity";
import {
  ACTIVITY_CATEGORY_LABELS,
  ACTIVITY_INCLUSIONS,
  ACTIVITY_SCHEDULE_OPTIONS,
  ACTIVITY_DURATION_OPTIONS,
  DEFAULT_ACTIVITY_FORM,
  ACTIVITY_VALIDATION,
} from "@/types/activity";

interface ActivityFormProps {
  mode: "create" | "edit";
}

export default function ActivityForm({ mode }: ActivityFormProps) {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [inclusionsOpen, setInclusionsOpen] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<ActivityFormData>({
    defaultValues: DEFAULT_ACTIVITY_FORM,
  });

  const watchedInclusions = watch("inclusions");
  const watchedImages = watch("existing_images");

  // Load activity data for edit mode
  useEffect(() => {
    if (mode === "edit" && id) {
      loadActivity();
    }
  }, [mode, id]);

  const loadActivity = async () => {
    try {
      setLoading(true);
      const response = await getActivity(id!);
      if (response.success) {
        const activity = response.data;
        reset({
          title: activity.title,
          category: activity.category,
          description: activity.description,
          duration: activity.duration,
          schedule: activity.schedule || "",
          pricing: activity.pricing,
          inclusions: activity.inclusions,
          images: [],
          existing_images: activity.images,
          status: activity.status,
          featured: activity.featured,
        });
      } else {
        toast.error(response.message);
        navigate("/admin/activities");
      }
    } catch (error) {
      console.error("Error loading activity:", error);
      toast.error("Failed to load activity");
      navigate("/admin/activities");
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: ActivityFormData) => {
    try {
      setSaving(true);

      // Upload new images first
      const uploadedImageUrls: string[] = [];
      if (data.images.length > 0) {
        setImageUploading(true);
        for (const file of data.images) {
          const uploadResult = await uploadActivityImage(file, id || "temp");
          if (uploadResult.success) {
            uploadedImageUrls.push(uploadResult.url);
          } else {
            toast.error(`Failed to upload image: ${file.name}`);
          }
        }
        setImageUploading(false);
      }

      // Combine existing and new images
      const allImages = [...data.existing_images, ...uploadedImageUrls];

      const activityData = {
        title: data.title,
        category: data.category,
        description: data.description,
        duration: data.duration,
        schedule: data.schedule || undefined,
        pricing: data.pricing,
        inclusions: data.inclusions,
        images: allImages,
        status: data.status,
        featured: data.featured,
      };

      let result;
      if (mode === "create") {
        result = await createActivity(activityData);
      } else {
        result = await updateActivity({ id: id!, ...activityData });
      }

      if (result.success) {
        toast.success(
          mode === "create"
            ? "Activity created successfully"
            : "Activity updated successfully"
        );
        navigate("/admin/activities");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error saving activity:", error);
      toast.error("Failed to save activity");
    } finally {
      setSaving(false);
      setImageUploading(false);
    }
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const currentImages = watch("images") || [];
    setValue("images", [...currentImages, ...files]);
  };

  // Handle image removal
  const handleImageRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const currentImages = watchedImages || [];
      const newImages = currentImages.filter((_, i) => i !== index);
      setValue("existing_images", newImages);
    } else {
      const currentImages = watch("images") || [];
      const newImages = currentImages.filter((_, i) => i !== index);
      setValue("images", newImages);
    }
  };

  // Handle inclusion toggle
  const handleInclusionToggle = (inclusion: string) => {
    const currentInclusions = watchedInclusions || [];
    const newInclusions = currentInclusions.includes(inclusion)
      ? currentInclusions.filter((item) => item !== inclusion)
      : [...currentInclusions, inclusion];
    setValue("inclusions", newInclusions);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-amber-600" />
        <span className="ml-2 text-gray-600">Loading activity...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate("/admin/activities")}
            className="p-2"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {mode === "create" ? "Add Activity" : "Edit Activity"}
            </h1>
            <p className="text-gray-600 mt-2">
              {mode === "create"
                ? "Create a new safari experience or tour"
                : "Update activity details and settings"}
            </p>
          </div>
        </div>
        <Button
          onClick={handleSubmit(onSubmit)}
          disabled={saving || imageUploading}
          className="bg-amber-600 hover:bg-amber-700"
        >
          {saving ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              {mode === "create" ? "Creating..." : "Updating..."}
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              {mode === "create" ? "Create Activity" : "Update Activity"}
            </>
          )}
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                {...register("title", {
                  required: "Title is required",
                  minLength: {
                    value: ACTIVITY_VALIDATION.title.minLength,
                    message: `Title must be at least ${ACTIVITY_VALIDATION.title.minLength} characters`,
                  },
                  maxLength: {
                    value: ACTIVITY_VALIDATION.title.maxLength,
                    message: `Title must be less than ${ACTIVITY_VALIDATION.title.maxLength} characters`,
                  },
                })}
                placeholder="e.g., Game Drive – Morning Safari"
              />
              {errors.title && (
                <p className="text-sm text-red-600">{errors.title.message}</p>
              )}
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Controller
                name="category"
                control={control}
                rules={{ required: "Category is required" }}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ACTIVITY_CATEGORY_LABELS).map(
                        ([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.category && (
                <p className="text-sm text-red-600">
                  {errors.category.message}
                </p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                {...register("description", {
                  required: "Description is required",
                  minLength: {
                    value: ACTIVITY_VALIDATION.description.minLength,
                    message: `Description must be at least ${ACTIVITY_VALIDATION.description.minLength} characters`,
                  },
                  maxLength: {
                    value: ACTIVITY_VALIDATION.description.maxLength,
                    message: `Description must be less than ${ACTIVITY_VALIDATION.description.maxLength} characters`,
                  },
                })}
                placeholder="Detailed description of the activity..."
                rows={4}
              />
              {errors.description && (
                <p className="text-sm text-red-600">
                  {errors.description.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Activity Details */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Duration */}
            <div className="space-y-2">
              <Label htmlFor="duration">Duration *</Label>
              <Controller
                name="duration"
                control={control}
                rules={{ required: "Duration is required" }}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      {ACTIVITY_DURATION_OPTIONS.map((duration) => (
                        <SelectItem key={duration} value={duration}>
                          {duration}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.duration && (
                <p className="text-sm text-red-600">
                  {errors.duration.message}
                </p>
              )}
            </div>

            {/* Schedule */}
            <div className="space-y-2">
              <Label htmlFor="schedule">Schedule</Label>
              <Controller
                name="schedule"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select schedule (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No specific schedule</SelectItem>
                      {ACTIVITY_SCHEDULE_OPTIONS.map((schedule) => (
                        <SelectItem key={schedule} value={schedule}>
                          {schedule}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Pricing */}
            <div className="space-y-2">
              <Label htmlFor="pricing">Pricing *</Label>
              <Input
                id="pricing"
                {...register("pricing", { required: "Pricing is required" })}
                placeholder="e.g., $50 per person, $120 per group"
              />
              {errors.pricing && (
                <p className="text-sm text-red-600">{errors.pricing.message}</p>
              )}
            </div>

            {/* Inclusions */}
            <div className="space-y-2">
              <Label>Inclusions</Label>
              <Popover open={inclusionsOpen} onOpenChange={setInclusionsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={inclusionsOpen}
                    className="w-full justify-between"
                  >
                    {watchedInclusions && watchedInclusions.length > 0
                      ? `${watchedInclusions.length} inclusion${
                          watchedInclusions.length > 1 ? "s" : ""
                        } selected`
                      : "Select inclusions..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Search inclusions..." />
                    <CommandEmpty>No inclusion found.</CommandEmpty>
                    <CommandGroup className="max-h-64 overflow-auto">
                      {ACTIVITY_INCLUSIONS.map((inclusion) => (
                        <CommandItem
                          key={inclusion}
                          onSelect={() => handleInclusionToggle(inclusion)}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              watchedInclusions?.includes(inclusion)
                                ? "opacity-100"
                                : "opacity-0"
                            }`}
                          />
                          {inclusion}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>

              {/* Selected Inclusions */}
              {watchedInclusions && watchedInclusions.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {watchedInclusions.map((inclusion, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-1 bg-amber-100 text-amber-800 px-2 py-1 rounded-md text-sm"
                    >
                      {inclusion}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-amber-200"
                        onClick={() => handleInclusionToggle(inclusion)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle>Images</CardTitle>
            <CardDescription>
              Upload high-quality images of the activity. At least one image is
              required.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Image Upload */}
            <div className="space-y-2">
              <Label htmlFor="images">Upload Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <Label htmlFor="image-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Click to upload images
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, GIF up to 10MB each
                    </span>
                  </Label>
                  <Input
                    id="image-upload"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            {/* Existing Images Preview */}
            {watchedImages && watchedImages.length > 0 && (
              <div className="space-y-2">
                <Label>Current Images</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {watchedImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={imageUrl}
                        alt={`Activity image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleImageRemove(index, true)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* New Images Preview */}
            {watch("images") && watch("images").length > 0 && (
              <div className="space-y-2">
                <Label>New Images</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {watch("images").map((file, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`New image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleImageRemove(index, false)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Image validation error */}
            {errors.images && (
              <p className="text-sm text-red-600">{errors.images.message}</p>
            )}
          </CardContent>
        </Card>

        {/* Status and Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Status and Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="unpublished">Unpublished</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
              <p className="text-sm text-gray-500">
                Only published activities will appear on the public website.
              </p>
            </div>

            {/* Featured */}
            <div className="flex items-center space-x-2">
              <Controller
                name="featured"
                control={control}
                render={({ field }) => (
                  <Switch
                    id="featured"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
              <div className="space-y-1">
                <Label htmlFor="featured">Featured Activity</Label>
                <p className="text-sm text-gray-500">
                  Featured activities are highlighted on the homepage and in
                  search results.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate("/admin/activities")}
              >
                Cancel
              </Button>
              <div className="flex items-center space-x-2">
                {mode === "edit" && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setValue("status", "draft");
                      handleSubmit(onSubmit)();
                    }}
                    disabled={saving || imageUploading}
                  >
                    Save as Draft
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={saving || imageUploading}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  {saving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      {mode === "create" ? "Creating..." : "Updating..."}
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      {mode === "create"
                        ? "Create Activity"
                        : "Update Activity"}
                    </>
                  )}
                </Button>
              </div>
            </div>

            {imageUploading && (
              <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                <div className="flex items-center">
                  <Loader2 className="w-4 h-4 mr-2 animate-spin text-amber-600" />
                  <span className="text-sm text-amber-800">
                    Uploading images...
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
